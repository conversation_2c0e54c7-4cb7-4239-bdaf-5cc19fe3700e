'use client';

import { useClientNavigation } from '@/hooks/use-client-navigation';
import { useCallback } from 'react';
import { usePathname } from 'next/navigation';

interface BottomNavItemClientProps {
  IconComponent: React.ComponentType<{
    fill?: string;
    className?: string;
    type?: 'filled' | 'outline';
    [key: string]: unknown;
  }>;
  label: string;
  href: string;
  isSelected: boolean;
  supportsFilledState?: boolean;
  badge?: string;
  iconProps?: {
    [key: string]: unknown;
  };
}

export default function BottomNavItemClient(props: BottomNavItemClientProps) {
  const { navigateTo } = useClientNavigation();
  const pathname = usePathname();

  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();

      // Check if this is the home button and we're already on the home page
      const isHomeButton = props.label === 'Home';
      const isOnHomePage = pathname === '/' || pathname.startsWith('/?');

      if (isHomeButton && isOnHomePage) {
        // Scroll to top instead of navigating
        const mainPageElement = document.querySelector('.main-page');
        if (mainPageElement) {
          mainPageElement.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
        }
        return;
      }

      // Navigate immediately for better UX
      navigateTo(props.href, { immediate: true });
    },
    [navigateTo, props.href, props.label, pathname]
  );

  return (
    <div className="relative">
      {props.badge && (
        <div className="bg-[var(--theme-purple-400)] text-content-primary text-[calc(var(--scale)*8)] rounded-full size-14 flex justify-center items-center absolute top-10 right-4">
          <p className="text-center ">{props.badge}</p>
        </div>
      )}

      <button
        onClick={handleClick}
        className="flex flex-col items-center p-2 rounded-lg transition-colors py-12 flex-1 cursor-pointer"
      >
        <props.IconComponent
          fill={props.isSelected ? 'var(--theme-purple-400)' : 'var(--content-tertiary)'}
          className="size-20 transition-colors duration-300 cursor-pointer"
          type={props.supportsFilledState ? (props.isSelected ? 'filled' : 'outline') : undefined}
          {...props.iconProps}
        />
        <span
          className={`text-xs font-medium cursor-pointer ${props.isSelected ? 'text-white' : 'text-content-tertiary'}`}
        >
          {props.label}
        </span>
      </button>
    </div>
  );
}
